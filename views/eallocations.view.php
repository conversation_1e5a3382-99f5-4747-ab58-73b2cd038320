<?php
#region region DOCS
/** @var Allocation $modallocation */
/** @var string $idallocation */
/** @var AllocationItem[] $listaAllocationItems */

use App\classes\Allocation;
use App\classes\AllocationItem;

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Editar Asignación</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h1 class="page-header mb-0">Editar Asignación</h1>
            <a href="allocations" class="btn btn-default">
                <i class="fa fa-arrow-left me-1"></i> Regresar
            </a>
        </div>

        <hr>
        <!-- END page-header -->

        <!-- BEGIN tabs navigation -->
        <ul class="nav nav-tabs mb-3" id="allocationTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                    <i class="fa fa-edit me-1"></i> General
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="items-tab" data-bs-toggle="tab" data-bs-target="#items" type="button" role="tab" aria-controls="items" aria-selected="false">
                    <i class="fa fa-list me-1"></i> Items de Asignación
                </button>
            </li>
        </ul>
        <!-- END tabs navigation -->

        <!-- BEGIN tab content -->
        <div class="tab-content" id="allocationTabContent">
            <!-- BEGIN General tab -->
            <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                <?php #region region FORM ?>
        <form id="allocationForm">
            <input type="hidden" id="idallocation" name="idallocation" value="<?php echo @recover_var($idallocation) ?>">

            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <label class="form-label">Nombre:</label>
                    <input type="text" name="nombre" id="nombre" value="<?php echo @recover_var($modallocation->getNombre()) ?>" class="form-control" onclick="this.focus();this.select('')" autofocus/>
                </div>
                <!-- END text -->
                <!-- BEGIN porcentaje -->
                <div class="col-md-6 col-xs-12">
                    <label class="form-label">Porcentaje:</label>
                    <input type="number" name="porcentaje" id="porcentaje" value="<?php echo @recover_var($modallocation->getPorcentaje()) ?>" class="form-control" step="0.1" min="0" max="100" onclick="this.focus();this.select('')"/>
                </div>
                <!-- END porcentaje -->
            </div>
            <!-- END row -->

            <!-- BEGIN row -->
            <div class="row mt-3">
                <div class="col-md-12 col-xs-12">
                    <button type="submit" id="sub_mod_allocation" name="sub_mod_allocation" class="btn btn-success w-100">
                        <i class="fa fa-save me-1"></i> Actualizar Asignación
                    </button>
                </div>
            </div>
            <!-- END row -->
        </form>
        <?php #endregion form ?>
            </div>
            <!-- END General tab -->

            <!-- BEGIN Items tab -->
            <div class="tab-pane fade" id="items" role="tabpanel" aria-labelledby="items-tab">
                <!-- BEGIN AllocationItems Section -->

        <?php #region region FORM AllocationItem ?>
        <form id="addAllocationItemForm" class="mb-4">
            <input type="hidden" name="idallocation" value="<?php echo @recover_var($idallocation) ?>">

            <div class="row">
                <div class="col-md-3 col-xs-12 mb-3">
                    <label class="form-label" for="item_nombre">Nombre:</label>
                    <input type="text" name="item_nombre" id="item_nombre" class="form-control" required placeholder="Ej: Inversión A">
                </div>
                <div class="col-md-3 col-xs-12 mb-3">
                    <label class="form-label" for="item_porcentaje">Porcentaje:</label>
                    <input type="number" name="item_porcentaje" id="item_porcentaje" class="form-control" required step="0.1" min="0" max="100" placeholder="Ej: 25.5">
                </div>
                <div class="col-md-3 col-xs-12 mb-3">
                    <label class="form-label" for="item_valor">Valor:</label>
                    <input type="text" name="item_valor" id="item_valor" class="form-control" required placeholder="Ej: 500.000" data-type="currencysinsigno">
                </div>
                <div class="col-md-3 col-xs-12 mb-3 d-flex align-items-end">
                    <button type="submit" name="sub_add_allocation_item" class="btn btn-primary w-100">
                        <i class="fa fa-plus me-1"></i> Agregar Item
                    </button>
                </div>
            </div>
        </form>
        <?php #endregion form AllocationItem ?>

        <?php #region region PANEL AllocationItems ?>
        <div class="panel panel-inverse mt-3" id="allocationItemsPanel">
            <div class="panel-heading">
                <h4 class="panel-title d-flex justify-content-between align-items-center">
                    <span>Items de Asignación:</span>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-info ms-2 fs-12px">Total: <?php echo $totalAllocationItems; ?> items</span>
                        <span class="badge bg-warning ms-2 fs-12px">
                            Porcentaje: <?php echo number_format($sumaPorcentajes, 1); ?>%
                        </span>
                        <span class="badge bg-success ms-2 fs-12px">
                            Valor: <?php echo format_currency($sumaValores); ?>
                        </span>
                        <button type="button" class="btn btn-sm btn-primary ms-3" id="btnApplyRecommendedPercentages" title="Aplicar porcentajes recomendados a todos los items">
                            <i class="fa fa-check me-1"></i>Aplicar % Recomendado
                        </button>
                    </div>
                </h4>
            </div>
            <!-- BEGIN panel-body -->
            <div class="table-nowrap bg-gray-900" style="overflow: auto" id="allocationItemsTableContainer">
                <?php #region region TABLE AllocationItems ?>
                <table class="table table-sm table-hover">
                    <thead>
                        <tr>
                            <th class="w-50px">Acciones</th>
                            <th class="text-center">Nombre</th>
                            <th class="text-center">%</th>
                            <th class="text-center">% recomendado</th>
                            <th class="text-center">Bolsillo</th>
                            <th class="text-center">Valor</th>
                        </tr>
                    </thead>
                    <tbody class="fs-13px" id="allocationItemsTableBody">
                        <?php
                        if (!empty($listaAllocationItems)):
                            // Calculate recommended percentages using the new redistribution logic
                            $totalPercentage = 0;
                            $countNonZero = 0;

                            // First pass: calculate totals
                            foreach ($listaAllocationItems as $item) {
                                $percentage = $item->getPorcentaje() ?? 0;
                                $totalPercentage += $percentage;
                                if ($percentage > 0) {
                                    $countNonZero++;
                                }
                            }

                            // Calculate adjustment
                            $difference = 100 - $totalPercentage;
                            $adjustmentPerRecord = $countNonZero > 0 ? ($difference / $countNonZero) : 0;

                            // Calculate recommended percentages for each item
                            $recommendedPercentages = [];
                            foreach ($listaAllocationItems as $item) {
                                $percentage = $item->getPorcentaje() ?? 0;
                                if ($percentage === 0.0) {
                                    $recommendedPercentages[$item->getId()] = 0;
                                } else {
                                    $recommendedPercentages[$item->getId()] = $percentage + $adjustmentPerRecord;
                                }
                            }
                        ?>
                            <?php foreach ($listaAllocationItems as $item): ?>
                                <tr>
                                    <td class="align-middle text-center">
                                        <i class="fa fa-trash fa-md cursor-pointer text-danger"
                                           data-bs-toggle="modal"
                                           data-bs-target="#mdl_del_allocation_item"
                                           data-idallocationitem="<?php echo $item->getId(); ?>"
                                           title="Eliminar item"></i>
                                    </td>
                                    <td class="align-middle<?php
                                        // Apply conditional styling for Nombre column
                                        // Green color when both conditions are met: Bolsillo == Valor AND Valor > 0
                                        $valorBolsillo = $item->getValorBolsillo() ?? 0;
                                        $valor = $item->getValor() ?? 0;
                                        if ($valorBolsillo == $valor && $valor > 0) {
                                            echo ' text-success';
                                        }
                                    ?>"><?php echo htmlspecialchars($item->getNombre() ?? ''); ?></td>
                                    <td class="text-center align-middle">
                                        <span class="percentage-editable cursor-pointer"
                                              data-id="<?php echo $item->getId(); ?>"
                                              title="Doble clic para editar">
                                            <?php echo number_format($item->getPorcentaje(), 1); ?>%
                                        </span>
                                    </td>
                                    <td class="text-center align-middle text-dark"><?php echo number_format($recommendedPercentages[$item->getId()], 2); ?>%</td>
                                    <td class="text-end align-middle">
                                        <span class="bolsillo-editable cursor-pointer text-info"
                                              data-id="<?php echo $item->getId(); ?>"
                                              data-valor-bolsillo="<?php echo $item->getValorBolsillo() ?? 0; ?>"
                                              title="Doble clic para editar">
                                            <?php echo formatCurrencyConSigno($item->getValorBolsillo() ?? 0); ?>
                                        </span>
                                    </td>
                                    <td class="text-end align-middle">
                                        <span class="valor-editable cursor-pointer<?php
                                            // Apply green color when both conditions are met:
                                            // 1. Bolsillo >= Valor
                                            // 2. Valor > 0
                                            $valorBolsillo = $item->getValorBolsillo() ?? 0;
                                            $valor = $item->getValor() ?? 0;
                                            if ($valorBolsillo >= $valor && $valor > 0) {
                                                echo ' text-success';
                                            }
                                        ?>"
                                              data-id="<?php echo $item->getId(); ?>"
                                              data-valor="<?php echo $item->getValor(); ?>"
                                              title="Doble clic para editar">
                                            <?php echo formatCurrencyConSigno($item->getValor()); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center text-muted">No hay items de asignación registrados.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr class="fw-bold">
                            <td class="align-middle"></td>
                            <td class="text-end align-middle">Total:</td>
                            <td class="text-center align-middle">
                                <span class="<?php echo ($sumaPorcentajes == 100.0) ? 'text-success' : 'text-warning'; ?>">
                                    <?php echo number_format($sumaPorcentajes, 1); ?>%
                                </span>
                            </td>
                            <td class="text-center align-middle">
                                <span class="text-info">100.00%</span>
                            </td>
                            <td class="text-end align-middle">
                                <span class="text-info"><?php echo formatCurrencyConSigno($sumaValorBolsillo); ?></span>
                            </td>
                            <td class="text-end align-middle pe-2">
                                <!-- Valor total removed -->
                            </td>
                        </tr>
                        <tr class="fw-bold">
                            <td class="align-middle"></td>
                            <td class="text-end align-middle">Diferencia:</td>
                            <td class="text-center align-middle">
                                <span class="text-danger">
                                    <?php echo number_format($sumaPorcentajes - 100, 1); ?>%
                                </span>
                            </td>
                            <td class="text-center align-middle">
                                <span class="text-muted">-</span>
                            </td>
                            <td class="text-end align-middle">
                                <span class="text-muted">-</span>
                            </td>
                            <td class="text-end align-middle pe-2">
                                <!-- Empty -->
                            </td>
                        </tr>
                    </tfoot>
                </table>
                <?php #endregion table AllocationItems ?>
            </div>
            <!-- END panel-body -->
        </div>
        <?php #endregion panel AllocationItems ?>
        <!-- END AllocationItems Section -->
            </div>
            <!-- END Items tab -->
        </div>
        <!-- END tab content -->

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region MODAL mdl_del_allocation_item ?>
<div class="modal fade" id="mdl_del_allocation_item">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="deleteAllocationItemForm">
                <input type="hidden" name="idallocation" value="<?php echo @recover_var($idallocation) ?>">
                <input type="hidden" id="mdl_del_allocation_item_id" name="idallocationitem">

                <div class="modal-header">
                    <h4 class="modal-title">Eliminar Item de Asignación</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <p>¿Está seguro que desea eliminar este item de asignación?</p>
                    <p class="text-muted small">Esta acción no se puede deshacer.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fa fa-times me-1"></i> Cancelar
                    </button>
                    <button type="submit" name="sub_del_allocation_item" class="btn btn-danger">
                        <i class="fa fa-trash me-1"></i> Eliminar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php #endregion mdl_del_allocation_item ?>

<?php #region region CSS ?>
<style>
/* Simple Tab Styling */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    color: #6c757d;
    font-weight: 500;
    padding: 10px 16px;
    border: 1px solid transparent;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #0f766e;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 600;
}

.tab-content {
    background: #fff;
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 20px;
    border-radius: 0 0 6px 6px;
}

/* Dark mode adjustments */
.dark-mode .nav-tabs {
    border-bottom-color: #495057;
}

.dark-mode .nav-tabs .nav-link {
    color: #adb5bd;
}

.dark-mode .nav-tabs .nav-link:hover {
    border-color: #495057 #495057 #495057;
    color: #f8f9fa;
}

.dark-mode .nav-tabs .nav-link.active {
    background-color: #2d353c;
    border-color: #495057 #495057 #2d3748;
}

.dark-mode .tab-content {
    background: #2d353c;
    border-color: #495057;
    color: #e2e8f0;
}

/* Toast notification styles (same as epartido_probabilidades.view.php) */
.floating-badge-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    z-index: 9999;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    max-width: 350px;
    min-width: 200px;
    line-height: 1.4;
    word-wrap: break-word;
}

.floating-badge-toast.show {
    opacity: 1;
    transform: translateX(0);
}

.floating-badge-toast.hide {
    opacity: 0;
    transform: translateX(100%);
}

.floating-badge-toast.error {
    background-color: #dc3545;
}

/* Inline editing styles for percentage column */
.percentage-editable {
    border: 1px solid transparent;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.percentage-editable:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: #6c757d;
}

.dark-mode .percentage-editable:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: #495057;
}

/* Inline editing styles for valor column */
.valor-editable {
    border: 1px solid transparent;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.valor-editable:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: #6c757d;
}

.dark-mode .valor-editable:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: #495057;
}

/* Inline editing styles for bolsillo column */
.bolsillo-editable {
    border: 1px solid transparent;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.bolsillo-editable:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: #6c757d;
}

.dark-mode .bolsillo-editable:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: #495057;
}
</style>
<?php #endregion CSS ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<!-- Currency formatting script -->
<script src="resources/js/formatcurrency2.js"></script>

<script type="text/javascript">
    // Function to clean currency string to a numeric string for server
    function cleanCurrencyForServer(value) {
        if (value === null || value === undefined || String(value).trim() === '') return '';
        // Remove dots (thousand separators for es-CO)
        return String(value).replace(/\./g, '');
    }

    // Toast notification function (same as epartido_probabilidades.view.php)
    function showToastMessage(message, isError = false) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.classList.add('floating-badge-toast');
        if (isError) {
            toast.classList.add('error');
        }
        document.body.appendChild(toast);
        setTimeout(() => { toast.classList.add('show'); }, 10);
        setTimeout(() => {
            toast.classList.add('hide');
            setTimeout(() => { toast.remove(); }, 300);
        }, 3000);
    }

    // Function to refresh only the AllocationItems table section
    function refreshAllocationItemsTable() {
        const formData = new FormData();
        formData.append('action', 'get_allocation_items_data');
        formData.append('idallocation', document.getElementById('idallocation').value);

        fetch('editar-allocation', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                updateAllocationItemsTable(data.data);
            } else {
                console.error('Error refreshing table:', data.message);
                location.reload(); // Fallback to full page reload
            }
        })
        .catch(error => {
            console.error('Error refreshing table:', error);
            location.reload(); // Fallback to full page reload
        });
    }

    // Function to calculate recommended percentages based on the new redistribution logic
    function calculateRecommendedPercentages(items) {
        const recommendedPercentages = {};

        if (!items || items.length === 0) {
            return recommendedPercentages;
        }

        // Calculate total percentage and count non-zero items
        let totalPercentage = 0;
        let countNonZero = 0;

        items.forEach(item => {
            const percentage = item.porcentaje || 0;
            totalPercentage += percentage;
            if (percentage > 0) {
                countNonZero++;
            }
        });

        // Calculate adjustment per record
        const difference = 100 - totalPercentage;
        const adjustmentPerRecord = countNonZero > 0 ? (difference / countNonZero) : 0;

        // Calculate recommended percentage for each item
        items.forEach(item => {
            const percentage = item.porcentaje || 0;
            let recommendedPercentage;

            if (percentage === 0) {
                // If percentage is 0, recommended percentage is also 0
                recommendedPercentage = 0;
            } else {
                // If percentage > 0, add the adjustment
                recommendedPercentage = percentage + adjustmentPerRecord;
            }

            recommendedPercentages[item.id] = recommendedPercentage;
        });

        return recommendedPercentages;
    }

    // Function to update the AllocationItems table with new data
    function updateAllocationItemsTable(data) {
        // Update statistics in panel heading
        const panelTitle = document.querySelector('#allocationItemsPanel .panel-title');
        if (panelTitle) {
            const badgesHtml = `
                <span class="badge bg-info ms-2 fs-12px">Total: ${data.totalItems} items</span>
                <span class="badge bg-warning ms-2 fs-12px">Porcentaje: ${data.sumaPorcentajes.toFixed(1)}%</span>
                <span class="badge bg-success ms-2 fs-12px">Valor: ${formatCurrency(data.sumaValores)}</span>
                <button type="button" class="btn btn-sm btn-primary ms-3" id="btnApplyRecommendedPercentages" title="Aplicar porcentajes recomendados a todos los items">
                    <i class="fa fa-check me-1"></i>Aplicar % Recomendado
                </button>
            `;
            panelTitle.innerHTML = '<span>Items de Asignación:</span><div class="d-flex align-items-center">' + badgesHtml + '</div>';

            // Re-attach event handler for the button since it was recreated
            const btnApplyRecommendedPercentages = document.getElementById('btnApplyRecommendedPercentages');
            if (btnApplyRecommendedPercentages) {
                btnApplyRecommendedPercentages.addEventListener('click', applyRecommendedPercentages);
            }
        }

        // Update table body
        const tableBody = document.getElementById('allocationItemsTableBody');
        if (tableBody) {
            if (data.items.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No hay items de asignación registrados.</td></tr>';
            } else {
                let rowsHtml = '';

                // Calculate recommended percentages using the new logic
                const recommendedPercentages = calculateRecommendedPercentages(data.items);

                data.items.forEach(item => {
                    // Apply conditional styling for valor column
                    // Green color when both conditions are met: Bolsillo >= Valor AND Valor > 0
                    const valorBolsillo = item.valor_bolsillo || 0;
                    const valor = item.valor || 0;
                    const valorClass = (valorBolsillo >= valor && valor > 0) ? ' text-success' : '';

                    // Apply conditional styling for nombre column
                    // Green color when both conditions are met: Bolsillo == Valor AND Valor > 0
                    const nombreClass = (valorBolsillo == valor && valor > 0) ? ' text-success' : '';

                    // Get the recommended percentage for this item
                    const recommendedPercentage = recommendedPercentages[item.id] || 0;

                    rowsHtml += `
                        <tr>
                            <td class="align-middle text-center">
                                <i class="fa fa-trash fa-md cursor-pointer text-danger"
                                   data-bs-toggle="modal"
                                   data-bs-target="#mdl_del_allocation_item"
                                   data-idallocationitem="${item.id}"
                                   title="Eliminar item"></i>
                            </td>
                            <td class="align-middle${nombreClass}">${escapeHtml(item.nombre)}</td>
                            <td class="text-center align-middle">
                                <span class="percentage-editable cursor-pointer"
                                      data-id="${item.id}"
                                      title="Doble clic para editar">
                                    ${item.porcentaje.toFixed(1)}%
                                </span>
                            </td>
                            <td class="text-center align-middle text-dark">${recommendedPercentage.toFixed(2)}%</td>
                            <td class="text-end align-middle">
                                <span class="bolsillo-editable cursor-pointer text-info"
                                      data-id="${item.id}"
                                      data-valor-bolsillo="${item.valor_bolsillo}"
                                      title="Doble clic para editar">
                                    ${formatCurrency(item.valor_bolsillo)}
                                </span>
                            </td>
                            <td class="text-end align-middle">
                                <span class="valor-editable cursor-pointer${valorClass}"
                                      data-id="${item.id}"
                                      data-valor="${item.valor}"
                                      title="Doble clic para editar">
                                    ${formatCurrency(item.valor)}
                                </span>
                            </td>
                        </tr>
                    `;
                });
                tableBody.innerHTML = rowsHtml;
            }
        }

        // Update table footer
        const tableFooter = document.querySelector('#allocationItemsTableContainer tfoot');
        if (tableFooter) {
            const percentageClass = (data.sumaPorcentajes === 100.0) ? 'text-success' : 'text-warning';
            const difference = data.sumaPorcentajes - 100;
            tableFooter.innerHTML = `
                <tr class="fw-bold">
                    <td class="align-middle"></td>
                    <td class="text-end align-middle">Total:</td>
                    <td class="text-center align-middle">
                        <span class="${percentageClass}">${data.sumaPorcentajes.toFixed(1)}%</span>
                    </td>
                    <td class="text-center align-middle">
                        <span class="text-info">100.00%</span>
                    </td>
                    <td class="text-end align-middle">
                        <span class="text-info">${formatCurrency(data.sumaValorBolsillo)}</span>
                    </td>
                    <td class="text-end align-middle pe-2">
                        <!-- Valor total removed -->
                    </td>
                </tr>
                <tr class="fw-bold">
                    <td class="align-middle"></td>
                    <td class="text-end align-middle">Diferencia:</td>
                    <td class="text-center align-middle">
                        <span class="text-danger">${difference.toFixed(1)}%</span>
                    </td>
                    <td class="text-center align-middle">
                        <span class="text-muted">-</span>
                    </td>
                    <td class="text-end align-middle">
                        <span class="text-muted">-</span>
                    </td>
                    <td class="text-end align-middle pe-2">
                        <!-- Empty -->
                    </td>
                </tr>
            `;
        }
    }

    // Helper function to format currency (Colombian peso format)
    function formatCurrency(value) {
        if (value === null || value === undefined || value === 0) return '$0';
        return '$' + Math.round(value).toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    }

    // Helper function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Function to update recommended percentages in real-time (without AJAX)
    function updateRecommendedPercentagesInTable() {
        const tableBody = document.getElementById('allocationItemsTableBody');
        if (!tableBody) return;

        // Get all percentage values from the current table
        const items = [];
        const rows = tableBody.querySelectorAll('tr');

        rows.forEach(row => {
            const percentageSpan = row.querySelector('.percentage-editable');
            const recommendedCell = row.querySelector('td:nth-child(4)'); // "% recomendado" column

            if (percentageSpan && recommendedCell) {
                const id = percentageSpan.getAttribute('data-id');
                const percentageText = percentageSpan.textContent.trim();
                const percentage = parseFloat(percentageText.replace('%', '')) || 0;

                items.push({
                    id: id,
                    porcentaje: percentage,
                    recommendedCell: recommendedCell
                });
            }
        });

        // Calculate recommended percentages using the new logic
        const recommendedPercentages = calculateRecommendedPercentages(items);

        // Update the recommended percentage cells
        items.forEach(item => {
            const recommendedPercentage = recommendedPercentages[item.id] || 0;
            item.recommendedCell.innerHTML = `${recommendedPercentage.toFixed(2)}%`;
        });
    }

    // Function to apply recommended percentages to actual percentages (bulk update)
    function applyRecommendedPercentages() {
        const tableBody = document.getElementById('allocationItemsTableBody');
        if (!tableBody) {
            showToastMessage('No se encontró la tabla de items', true);
            return;
        }

        // Get all items from the current table
        const items = [];
        const rows = tableBody.querySelectorAll('tr');

        rows.forEach(row => {
            const percentageSpan = row.querySelector('.percentage-editable');
            const recommendedCell = row.querySelector('td:nth-child(4)'); // "% recomendado" column

            if (percentageSpan && recommendedCell) {
                const id = percentageSpan.getAttribute('data-id');
                const percentageText = percentageSpan.textContent.trim();
                const currentPercentage = parseFloat(percentageText.replace('%', '')) || 0;
                const recommendedText = recommendedCell.textContent.trim();
                const recommendedPercentage = parseFloat(recommendedText.replace('%', '')) || 0;

                items.push({
                    id: id,
                    currentPercentage: currentPercentage,
                    recommendedPercentage: recommendedPercentage
                });
            }
        });

        if (items.length === 0) {
            showToastMessage('No hay items para actualizar', true);
            return;
        }

        // Filter items that need updating (where current != recommended)
        const itemsToUpdate = items.filter(item =>
            Math.abs(item.currentPercentage - item.recommendedPercentage) > 0.001 // Use small tolerance for floating point comparison
        );

        if (itemsToUpdate.length === 0) {
            showToastMessage('Los porcentajes ya están aplicados correctamente', false);
            return;
        }

        // Validate that the sum of recommended percentages equals 100% (sanity check)
        const totalRecommended = items.reduce((sum, item) => sum + item.recommendedPercentage, 0);
        if (Math.abs(totalRecommended - 100) > 0.1) {
            showToastMessage('Error: Los porcentajes recomendados no suman 100%. Por favor, recargue la página.', true);
            return;
        }

        // Show confirmation dialog using SweetAlert v1
        swal({
            title: '¿Aplicar porcentajes recomendados?',
            text: `¿Está seguro que desea aplicar los porcentajes recomendados a todos los items? Esto actualizará los valores de % para que sumen 100%. Se actualizarán ${itemsToUpdate.length} items.`,
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'Cancelar',
                    value: null,
                    visible: true,
                    className: 'btn btn-secondary',
                    closeModal: true
                },
                confirm: {
                    text: 'Sí, aplicar',
                    value: true,
                    visible: true,
                    className: 'btn btn-primary',
                    closeModal: true
                }
            }
        }).then((willApply) => {
            if (willApply) {
                performBulkUpdate(itemsToUpdate);
            }
        });
    }

    // Function to perform the bulk update AJAX request
    function performBulkUpdate(itemsToUpdate) {
        const button = document.getElementById('btnApplyRecommendedPercentages');

        // Disable button during request
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Aplicando...';
        }

        // Prepare the data for bulk update
        const updateData = itemsToUpdate.map(item => ({
            id: item.id,
            porcentaje: item.recommendedPercentage
        }));

        const formData = new FormData();
        formData.append('action', 'update_allocation_items_percentages_bulk');
        formData.append('idallocation', document.getElementById('idallocation').value);
        formData.append('items', JSON.stringify(updateData));

        fetch('editar-allocation', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showToastMessage('Porcentajes aplicados correctamente');
                // Refresh the table to show updated values
                refreshAllocationItemsTable();
            } else if (data.status === 'warning') {
                showToastMessage(data.message, false); // Show as info/warning, not error
                // Still refresh since some items were updated
                refreshAllocationItemsTable();
            } else {
                showToastMessage(data.message || 'Error al aplicar porcentajes', true);
            }
        })
        .catch(error => {
            console.error('Error applying percentages:', error);
            showToastMessage('Error al procesar la solicitud', true);
        })
        .finally(() => {
            // Re-enable button
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fa fa-check me-1"></i>Aplicar % Recomendado';
            }
        });
    }

    // Function to update allocation item percentage via AJAX
    function updateAllocationItemPercentage(id, newPercentage) {
        const formData = new FormData();
        formData.append('action', 'update_allocation_item_percentage');
        formData.append('idallocationitem', id);
        formData.append('porcentaje', newPercentage);

        return fetch('editar-allocation', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Refresh the table to update totals
                refreshAllocationItemsTable();
                return data;
            } else {
                throw new Error(data.message || 'Error updating percentage');
            }
        });
    }

    // Function to update allocation item valor via AJAX
    function updateAllocationItemValor(id, newValor) {
        const formData = new FormData();
        formData.append('action', 'update_allocation_item_valor');
        formData.append('idallocationitem', id);
        formData.append('valor', newValor);

        return fetch('editar-allocation', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Refresh the table to update totals
                refreshAllocationItemsTable();
                return data;
            } else {
                throw new Error(data.message || 'Error updating valor');
            }
        });
    }

    // Function to update allocation item valor_bolsillo via AJAX
    function updateAllocationItemValorBolsillo(id, newValorBolsillo) {
        const formData = new FormData();
        formData.append('action', 'update_allocation_item_valor_bolsillo');
        formData.append('idallocationitem', id);
        formData.append('valor_bolsillo', newValorBolsillo);

        return fetch('editar-allocation', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Refresh the table to update totals
                refreshAllocationItemsTable();
                return data;
            } else {
                throw new Error(data.message || 'Error updating valor bolsillo');
            }
        });
    }

    // Function to attach inline editing functionality to percentage cells
    function attachInlineEditPercentage(container) {
        container.addEventListener('dblclick', function(e) {
            const span = e.target.closest('.percentage-editable');
            if (!span) return;
            if (span.dataset.editing === '1') return;

            span.dataset.editing = '1';
            const id = span.getAttribute('data-id');
            const text = span.textContent.trim();
            const currentValue = parseFloat(text.replace('%', ''));

            const input = document.createElement('input');
            input.type = 'number';
            input.className = 'form-control form-control-sm';
            input.style.width = '80px';
            input.style.textAlign = 'center';
            input.value = currentValue;
            input.min = '0';
            input.max = '100';
            input.step = '0.1';

            span.replaceWith(input);
            input.focus();
            input.select();

            let replaced = false; // Flag to prevent double replacement

            const cancelToSpan = () => {
                if (replaced) return; // Prevent double replacement
                replaced = true;
                const newSpan = document.createElement('span');
                newSpan.className = 'percentage-editable cursor-pointer';
                newSpan.setAttribute('data-id', id);
                newSpan.setAttribute('title', 'Doble clic para editar');
                newSpan.textContent = text;
                input.replaceWith(newSpan);
            };

            const commit = () => {
                if (replaced) return; // Prevent double replacement
                const newValue = parseFloat(input.value);

                // Validate input
                if (isNaN(newValue) || newValue < 0 || newValue > 100) {
                    showToastMessage('El porcentaje debe estar entre 0 y 100', true);
                    cancelToSpan();
                    return;
                }

                if (newValue === currentValue) {
                    cancelToSpan();
                    return;
                }

                // First, update the span with the new value for immediate visual feedback
                replaced = true;
                const newSpan = document.createElement('span');
                newSpan.className = 'percentage-editable cursor-pointer';
                newSpan.setAttribute('data-id', id);
                newSpan.setAttribute('title', 'Doble clic para editar');
                newSpan.textContent = newValue.toFixed(1) + '%';
                input.replaceWith(newSpan);

                // Update recommended percentages in real-time
                updateRecommendedPercentagesInTable();

                // Then make the AJAX call to save to database
                updateAllocationItemPercentage(id, newValue).then(data => {
                    showToastMessage('Porcentaje actualizado correctamente');
                    // The table will be refreshed by the AJAX response, which will maintain the new calculations
                }).catch(error => {
                    showToastMessage(error.message || 'Error al actualizar porcentaje', true);
                    // On error, we could revert the changes, but the refreshAllocationItemsTable()
                    // call in updateAllocationItemPercentage will restore the correct values from server
                });
            };

            input.addEventListener('keydown', function(ev) {
                if (ev.key === 'Enter') {
                    ev.preventDefault();
                    commit();
                }
                if (ev.key === 'Escape') {
                    ev.preventDefault();
                    cancelToSpan();
                }
            });

            input.addEventListener('blur', cancelToSpan);
        });
    }

    // Function to attach inline editing functionality to valor cells
    function attachInlineEditValor(container) {
        container.addEventListener('dblclick', function(e) {
            const span = e.target.closest('.valor-editable');
            if (!span) return;
            if (span.dataset.editing === '1') return;

            span.dataset.editing = '1';
            const id = span.getAttribute('data-id');
            const currentValor = parseFloat(span.getAttribute('data-valor')) || 0;
            const text = span.textContent.trim();

            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'form-control form-control-sm';
            input.style.width = '120px';
            input.style.textAlign = 'right';
            input.value = Math.round(currentValor).toString();
            input.setAttribute('data-type', 'currencysinsigno');

            span.replaceWith(input);

            // Apply currency formatting to the input
            if (typeof formatCurrency2 !== 'undefined') {
                formatCurrency2();
            }

            input.focus();
            input.select();

            let replaced = false; // Flag to prevent double replacement

            const cancelToSpan = () => {
                if (replaced) return; // Prevent double replacement
                replaced = true;
                const newSpan = document.createElement('span');
                newSpan.className = 'valor-editable cursor-pointer';
                newSpan.setAttribute('data-id', id);
                newSpan.setAttribute('data-valor', currentValor);
                newSpan.setAttribute('title', 'Doble clic para editar');
                newSpan.textContent = text;
                input.replaceWith(newSpan);
            };

            const commit = () => {
                if (replaced) return; // Prevent double replacement
                const cleanedValue = cleanCurrencyForServer(input.value);
                const newValue = parseFloat(cleanedValue) || 0;

                if (newValue === currentValor) {
                    cancelToSpan();
                    return;
                }

                updateAllocationItemValor(id, newValue).then(data => {
                    if (replaced) return; // Prevent double replacement
                    replaced = true;
                    const newSpan = document.createElement('span');
                    newSpan.className = 'valor-editable cursor-pointer';
                    newSpan.setAttribute('data-id', id);
                    newSpan.setAttribute('data-valor', newValue);
                    newSpan.setAttribute('title', 'Doble clic para editar');
                    newSpan.textContent = formatCurrency(newValue);
                    input.replaceWith(newSpan);
                    showToastMessage('Valor actualizado correctamente');
                }).catch(error => {
                    showToastMessage(error.message || 'Error al actualizar valor', true);
                    cancelToSpan();
                });
            };

            input.addEventListener('keydown', function(ev) {
                if (ev.key === 'Enter') {
                    ev.preventDefault();
                    commit();
                }
                if (ev.key === 'Escape') {
                    ev.preventDefault();
                    cancelToSpan();
                }
            });

            input.addEventListener('blur', cancelToSpan);
        });
    }

    // Function to attach inline editing functionality to bolsillo cells
    function attachInlineEditBolsillo(container) {
        container.addEventListener('dblclick', function(e) {
            const span = e.target.closest('.bolsillo-editable');
            if (!span) return;
            if (span.dataset.editing === '1') return;

            span.dataset.editing = '1';
            const id = span.getAttribute('data-id');
            const currentValorBolsillo = parseFloat(span.getAttribute('data-valor-bolsillo')) || 0;
            const text = span.textContent.trim();

            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'form-control form-control-sm';
            input.style.width = '120px';
            input.style.textAlign = 'right';
            input.value = Math.round(currentValorBolsillo).toString();
            input.setAttribute('data-type', 'currencysinsigno');

            // Apply currency formatting to the input
            if (typeof formatCurrency2 === 'function') {
                formatCurrency2(input);
            }

            span.replaceWith(input);
            input.focus();
            input.select();

            let replaced = false; // Flag to prevent double replacement

            const cancelToSpan = () => {
                if (replaced) return; // Prevent double replacement
                replaced = true;
                const newSpan = document.createElement('span');
                newSpan.className = 'bolsillo-editable cursor-pointer text-info';
                newSpan.setAttribute('data-id', id);
                newSpan.setAttribute('data-valor-bolsillo', currentValorBolsillo);
                newSpan.setAttribute('title', 'Doble clic para editar');
                newSpan.textContent = text;
                input.replaceWith(newSpan);
            };

            const commit = () => {
                if (replaced) return; // Prevent double replacement
                const cleanedValue = cleanCurrencyForServer(input.value);
                const newValue = parseFloat(cleanedValue) || 0;

                if (newValue === currentValorBolsillo) {
                    cancelToSpan();
                    return;
                }

                updateAllocationItemValorBolsillo(id, newValue).then(data => {
                    if (replaced) return; // Prevent double replacement
                    replaced = true;
                    const newSpan = document.createElement('span');
                    newSpan.className = 'bolsillo-editable cursor-pointer text-info';
                    newSpan.setAttribute('data-id', id);
                    newSpan.setAttribute('data-valor-bolsillo', newValue);
                    newSpan.setAttribute('title', 'Doble clic para editar');
                    newSpan.textContent = formatCurrency(newValue);
                    input.replaceWith(newSpan);
                    showToastMessage('Valor bolsillo actualizado correctamente');
                }).catch(error => {
                    showToastMessage(error.message || 'Error al actualizar valor bolsillo', true);
                    cancelToSpan();
                });
            };

            input.addEventListener('keydown', function(ev) {
                if (ev.key === 'Enter') {
                    ev.preventDefault();
                    commit();
                }
                if (ev.key === 'Escape') {
                    ev.preventDefault();
                    cancelToSpan();
                }
            });

            input.addEventListener('blur', cancelToSpan);
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Handle allocation form submission
        const allocationForm = document.getElementById('allocationForm');
        if (allocationForm) {
            allocationForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                formData.append('ajax_check', '1');
                formData.append('sub_mod_allocation', '1');

                fetch('editar-allocation', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToastMessage(data.message);
                    } else {
                        showToastMessage(data.message, true);
                    }
                })
                .catch(error => {
                    showToastMessage('Error al procesar la solicitud', true);
                });
            });
        }

        // Handle allocation item form submission
        const addAllocationItemForm = document.getElementById('addAllocationItemForm');
        const itemValorInput = document.getElementById('item_valor');

        if (addAllocationItemForm && itemValorInput) {
            // Function to submit the form
            function submitAllocationItemForm() {
                // Clean currency format before submitting
                const originalValue = itemValorInput.value;
                itemValorInput.value = cleanCurrencyForServer(originalValue);

                const formData = new FormData(addAllocationItemForm);
                formData.append('ajax_check', '1');
                formData.append('sub_add_allocation_item', '1');

                fetch('editar-allocation', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToastMessage(data.message);
                        // Clear form and refresh only the items table section
                        addAllocationItemForm.reset();
                        refreshAllocationItemsTable();
                    } else {
                        showToastMessage(data.message, true);
                        // Restore original value if error
                        itemValorInput.value = originalValue;
                    }
                })
                .catch(error => {
                    showToastMessage('Error al procesar la solicitud', true);
                    // Restore original value if error
                    itemValorInput.value = originalValue;
                });
            }

            // Handle form submission via button click
            addAllocationItemForm.addEventListener('submit', function(e) {
                e.preventDefault();
                submitAllocationItemForm();
            });

            // Handle Enter key submission on valor field
            itemValorInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    submitAllocationItemForm();
                }
            });
        }

        // Handle delete allocation item form submission
        const deleteAllocationItemForm = document.getElementById('deleteAllocationItemForm');
        if (deleteAllocationItemForm) {
            deleteAllocationItemForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                formData.append('ajax_check', '1');
                formData.append('sub_del_allocation_item', '1');

                fetch('editar-allocation', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToastMessage(data.message);
                        // Close modal and refresh only the items table section
                        const modal = bootstrap.Modal.getInstance(document.getElementById('mdl_del_allocation_item'));
                        modal.hide();
                        // Instead of full page reload, refresh just the items tab content
                        refreshAllocationItemsTable();
                    } else {
                        showToastMessage(data.message, true);
                    }
                })
                .catch(error => {
                    showToastMessage('Error al procesar la solicitud', true);
                });
            });
        }

        // Initialize inline editing for percentage cells
        const allocationItemsTableContainer = document.getElementById('allocationItemsTableContainer');
        if (allocationItemsTableContainer) {
            attachInlineEditPercentage(allocationItemsTableContainer);
            attachInlineEditValor(allocationItemsTableContainer);
            attachInlineEditBolsillo(allocationItemsTableContainer);
        }

        // Add event handler for "Aplicar % Recomendado" button
        const btnApplyRecommendedPercentages = document.getElementById('btnApplyRecommendedPercentages');
        if (btnApplyRecommendedPercentages) {
            btnApplyRecommendedPercentages.addEventListener('click', applyRecommendedPercentages);
        }

        // Update recommended percentages on page load (in case there are existing items)
        updateRecommendedPercentagesInTable();
    });
</script>

<?php #region region JS mdl_del_allocation_item ?>
<script type="text/javascript">
    $('#mdl_del_allocation_item').on('shown.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const recipient_idallocationitem = button.data('idallocationitem');

        const mdl_del_allocation_item_id = document.getElementById('mdl_del_allocation_item_id');
        mdl_del_allocation_item_id.value = recipient_idallocationitem;
    });
</script>
<?php #endregion js mdl_del_allocation_item ?>
<?php #endregion js ?>

</body>
</html>
