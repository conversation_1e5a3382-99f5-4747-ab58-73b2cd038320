<?php
#region region DOCS
/** @var string $idgaming */
/** @var int $tabselected */
/** @var Gaming $modgaming */
/** @var GamingKeybind $newgamingkeybind */
/** @var Platform[] $platforms */
/** @var GamingKeybind[] $gaming_keybinds */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>My Dash | Gaming</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<?php #region region CSS select choices.js  ?>
	<link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/choices.min.css">
	<link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/fab_choices.css">
	<?php #endregion CSS select choices.js  ?>
	<style>
        /* --- Cursor Pointer --- */
        /* Aplica cursor pointer a las etiquetas Y a los inputs de ambos switches */
        label[for="usa_borderlessapp"],
        input[id="usa_borderlessapp"].form-check-input,
        label[for="en_standby"],
        input[id="en_standby"].form-check-input {
            cursor: pointer;
        }

        /* --- Estilo Rojo para Switch Standby (Activado) --- */
        /* Selecciona el input del switch 'en_standby' cuando está ':checked' */
        input[id="en_standby"].form-check-input:checked {
            background-color: #dc3545; /* Color de fondo rojo (Bootstrap danger) */
            border-color: #dc3545; /* Color del borde rojo */
            /* Importante: Bootstrap 5 usa una imagen SVG para el círculo blanco (el 'thumb'). */
            /* Estas reglas cambian el color del fondo (el 'track') detrás del círculo cuando está activo. */
        }

        /* --- (Opcional) Estilo de Foco para Switch Standby Rojo --- */
        /* Cambia el color del resplandor (box-shadow) cuando el switch rojo está enfocado y activado */
        input[id="en_standby"].form-check-input:focus:checked {
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5); /* Sombra roja semitransparente */
        }

        /* --- Estilos para Input Method Radio Buttons --- */
        .btn-check:checked + .btn-outline-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: #fff;
        }

        .btn-check:focus + .btn-outline-primary {
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .btn-outline-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
            color: #fff;
        }

        /* --- Toast notification styles (from epartido_probabilidades.css) --- */
        .floating-badge-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            z-index: 9999;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .floating-badge-toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .floating-badge-toast.hide {
            opacity: 0;
            transform: translateX(100%);
        }
	</style>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center justify-content-between mb-3">
			<div>
				<h4 class="mb-1">Editar juego: <span class="text-primary"><?php echo @recover_var($modgaming->name) ?></span></h4>
				<p class="text-muted mb-0">
					<i class="fa fa-gamepad me-2"></i>
					Plataforma: <span class="badge bg-secondary"><?php echo @recover_var($modgaming->name_platform) ?></span>
				</p>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region FORM ?>
		<form action="egaming" method="POST">
			<input type="hidden" id="idgaming" name="idgaming" value="<?php echo @recover_var($idgaming) ?>">
			<input type="hidden" id="tabselected" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">
			
			<!-- BEGIN row -->
			<div class="row mt-3">
				<?php #region region LINK regresar ?>
				<div class="col-md-4 col-xs-12">
					<a href="agaming" class="btn btn-md btn-default w-100">
						Regresar
					</a>
				</div>
				<?php #endregion LINK regresar ?>
				<?php #region region SUBMIT sub_jugado ?>
				<div class="col-md-4 col-xs-12">
					<button type="submit" id="sub_jugado" name="sub_jugado" class="btn btn-md btn-success w-100">
						Jugado
					</button>
				</div>
				<?php #endregion SUBMIT sub_jugado ?>
				<?php #region region LINK desinstalar ?>
				<div class="col-md-4 col-xs-12">
					<a href="#mdl_desinstalar" data-bs-toggle="modal" class="btn btn-md btn-danger w-100">
						Desinstalar
					</a>
				</div>
				<?php #endregion LINK desinstalar ?>
			</div>
			<!-- END row -->
			<?php #region region NAVTAB HEAD gaming ?>
			<ul class="region_NAVTAB_HEAD nav nav-tabs fs-13px mt-3 no-border-radious">
				<li class="nav-item" onclick="tabselect(1)">
					<a href="#default-tab-1" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 1) ? "active" : ""; ?>">
						General
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(4)">
					<a href="#default-tab-4" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 4) ? "active" : ""; ?>">
						Keybinds Overview & Issues
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(2)">
					<a href="#default-tab-2" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 2) ? "active" : ""; ?>">
						Keybinds
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(3)">
					<a href="#default-tab-3" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 3) ? "active" : ""; ?>">
						Notas
					</a>
				</li>
			
			</ul>
			<div class="tab-content panel rounded-0 rounded-bottom">
				<?php #region region TAB general ?>
				<div class="tab-pane p-3 fade <?php echo ($tabselected == 1) ? "active show" : ""; ?>" id="default-tab-1">

					<!-- SECTION: Basic Information -->
					<div class="card mb-4">
						<div class="card-header bg-primary text-white">
							<h6 class="mb-0"><i class="fa fa-info-circle me-2"></i>Información Básica</h6>
						</div>
						<div class="card-body">
							<div class="row">
								<div class="col-md-6 col-xs-12">
									<label class="form-label">Nombre:</label>
									<input type="text" name="name" id="name" value="<?php echo @recover_var($modgaming->name) ?>" class="form-control" onclick="this.focus();this.select('');"/>
								</div>
								<div class="col-md-6 col-xs-12">
									<label class="form-label">Plataforma:</label>
									<select id="id_platform" name="id_platform" class="form-select form-select-choices">
										<option value="">--</option>
										<?php foreach ($platforms as $platform): ?>
											<option <?php @recover_var_list(ordena($modgaming->platform->id_platform), ordena($platform->id_platform)) ?> value="<?php echo limpiar_datos($platform->id_platform); ?>">
												<?php echo $platform->name; ?>
											</option>
										<?php endforeach; ?>
									</select>
								</div>
							</div>
						</div>
					</div>

					<!-- SECTION: Input Method -->
					<div class="card mb-4">
						<div class="card-header bg-info text-white">
							<h6 class="mb-0"><i class="fa fa-gamepad me-2"></i>Método de Entrada</h6>
						</div>
						<div class="card-body">
							<div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
								<?php
								$current_input = @recover_var($modgaming->input);
								// Default to 'kbm' if empty or null
								if (empty($current_input)) {
									$current_input = 'kbm';
								}
								?>
								<input type="radio" name="input" id="input_kbm" class="btn-check" value="kbm" <?php @recoverradio('kbm', $current_input); ?> />
								<label class="btn btn-outline-primary btn-lg w-50" for="input_kbm">
									<i class="fa fa-keyboard me-2"></i>Teclado + Mouse
								</label>
								<input type="radio" name="input" id="input_controller" class="btn-check" value="controller" <?php @recoverradio('controller', $current_input); ?> />
								<label class="btn btn-outline-primary btn-lg w-50" for="input_controller">
									<i class="fa fa-gamepad me-2"></i>Control
								</label>
							</div>
						</div>
					</div>

					<!-- SECTION: Game Settings -->
					<div class="card mb-4">
						<div class="card-header bg-success text-white">
							<h6 class="mb-0"><i class="fa fa-cogs me-2"></i>Configuraciones del Juego</h6>
						</div>
						<div class="card-body">
							<div class="row">
								<div class="col-md-6 col-xs-12">
									<div class="form-check form-switch mb-3">
										<input type="checkbox" id="usa_borderlessapp" name="usa_borderlessapp" <?php echo @recoverVarCheckbox($modgaming->usa_borderlessapp); ?> class="form-check-input">
										<label class="form-check-label" for="usa_borderlessapp">
											<strong>Usa Borderless App</strong>
											<small class="d-block text-muted">Aplicación para modo sin bordes</small>
										</label>
									</div>
								</div>
								<div class="col-md-6 col-xs-12">
									<div class="form-check form-switch mb-3">
										<input type="checkbox" id="listo_para_instalar" name="listo_para_instalar" <?php echo @recoverVarCheckbox($modgaming->listo_para_instalar); ?> class="form-check-input">
										<label class="form-check-label" for="listo_para_instalar">
											<strong>Listo para instalar</strong>
											<small class="d-block text-muted">El juego está preparado para instalación</small>
										</label>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- SECTION: Status Management -->
					<div class="card mb-4">
						<div class="card-header bg-warning text-dark">
							<h6 class="mb-0"><i class="fa fa-exclamation-triangle me-2"></i>Gestión de Estados</h6>
						</div>
						<div class="card-body">
							<div class="row">
								<div class="col-md-6 col-xs-12 mb-3">
									<label for="en_standby" class="form-label mb-2">
										<i class="fa fa-pause-circle me-1"></i>Estado Standby:
									</label>
									<div class="input-group">
										<div class="input-group-text">
											<input class="form-check-input mt-0" type="checkbox" id="en_standby" name="en_standby" <?php echo @recoverVarCheckbox($modgaming->en_standby); ?> title="Activar/Desactivar Standby">
										</div>
										<input type="text" class="form-control" id="texto_standby" name="texto_standby"
										       placeholder="Motivo o texto de standby (si está activo)..."
										       value="<?php echo htmlspecialchars(@recover_var($modgaming->texto_standby) ?? '', ENT_QUOTES); ?>"
										       aria-label="Texto de Standby">
									</div>
								</div>
								<div class="col-md-6 col-xs-12 mb-3">
									<label for="por_instalar" class="form-label mb-2">
										<i class="fa fa-download me-1"></i>Por instalar:
									</label>
									<div class="input-group">
										<div class="input-group-text">
											<input class="form-check-input mt-0" type="checkbox" id="por_instalar" name="por_instalar" <?php echo @recoverVarCheckbox($modgaming->por_instalar); ?> title="Activar/Desactivar Por Instalar">
										</div>
										<input type="text" class="form-control" id="texto_por_instalar" name="texto_por_instalar"
										       placeholder="Nota por instalar (si está activo)..."
										       value="<?php echo htmlspecialchars(@recover_var($modgaming->texto_por_instalar) ?? '', ENT_QUOTES); ?>"
										       aria-label="Texto de por instalar">
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- SECTION: Submit Button -->
					<div class="d-grid">
						<?php #region region SUBMIT sub_mod ?>
						<button type="submit" id="sub_mod" name="sub_mod" class="btn btn-success btn-lg">
							<i class="fa fa-save me-2"></i>Modificar Juego
						</button>
						<?php #endregion SUBMIT sub_mod ?>
					</div>
				</div>
				<?php #endregion TAB general ?>
				<?php #region region TAB keybinds ?>
				<div class="tab-pane fade <?php echo ($tabselected == 2) ? "active show" : ""; ?>" id="default-tab-2">
					<!-- BEGIN row -->
					<div class="row p-2">
						<!-- BEGIN text -->
						<div class="col-md-4 col-xs-12">
							<label class="form-label">Nota:</label>
							<input type="text" name="note_keybind" id="note_keybind" value="<?php echo @recover_var($newgamingkeybind->note) ?>" class="form-control"/>
						</div>
						<!-- END text -->
						<!-- BEGIN text -->
						<div class="col-md-4 col-xs-12">
							<label class="form-label">Keybind:</label>
							<input type="text" name="keybind_keybind" id="keybind_keybind" value="<?php echo @recover_var($newgamingkeybind->keybind) ?>" class="form-control"/>
						</div>
						<!-- END text -->
						<!-- BEGIN text -->
						<div class="col-md-4 col-xs-12">
							<div class="mb-3">
								<label class="form-label">Prioridad:</label>
								<input type="text" name="keybind_prioridad" id="keybind_prioridad" value="<?php echo @recover_var($newgamingkeybind->prioridad) ?>" class="form-control"/>
							</div>
						</div>
						<!-- END text -->
					</div>
					<!-- END row -->
					<!-- BEGIN row -->
					<div class="row p-2">
						<?php #region region SUBMIT sub_add_keybind ?>
						<div class="col-md-12 col-xs-12">
							<button type="submit" id="sub_add_keybind" name="sub_add_keybind" class="btn btn-xs btn-success w-100 no-border-radious">
								Agregar keybind
							</button>
						</div>
						<?php #endregion SUBMIT sub_add_keybind ?>
					</div>
					<!-- END row -->
					<?php #region region TABLE keybinds ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="text-center">Prioridad</th>
							<th class="text-start">Nota</th>
							<th class="text-center">Keybind</th>
						</tr>
						</thead>
						<tbody class="fs-12px">
						<?php #region region ARRAY keybinds ?>
						<?php foreach ($gaming_keybinds as $gaming_keybind): ?>
							<tr class="cursor-pointer" data-bs-toggle="modal"
							    data-bs-target="#mdl_revisar_keybind"
							    data-id_keybind="<?php echo limpiar_datos($gaming_keybind->id) ?>"
							    data-nota="<?php echo limpiar_datos($gaming_keybind->note) ?>"
							    data-keybind="<?php echo limpiar_datos($gaming_keybind->keybind) ?>"
							    data-prioridad="<?php echo limpiar_datos($gaming_keybind->prioridad) ?>"
							>
								<td class="text-center">#<?php echo $gaming_keybind->prioridad; ?></td>
								<td class="text-start"><?php echo $gaming_keybind->note; ?></td>
								<td><?php echo $gaming_keybind->keybind; ?></td>
							</tr>
						<?php endforeach; ?>
						<?php #endregion array keybinds ?>
						</tbody>
					</table>
					<?php #endregion table keybinds ?>
				</div>
				<?php #endregion TAB keybinds ?>
				<?php #region region TAB keybinds_overview_issues ?>
				<div class="tab-pane fade <?php echo ($tabselected == 4) ? "active show" : ""; ?>" id="default-tab-4">
					<div class="row p-2">
						<?php #region Left Half: All Gaming Keybinds List ?>
						<div class="col-md-6 col-xs-12">
							<h5>All Keybinds</h5>
							<?php if (!empty($gaming_keybinds)): ?>
								<div class="table-responsive">
									<table class="table table-hover table-sm">
										<thead>
										<tr>
											<th class="text-start">Note</th>
											<th>Keybind</th>
										</tr>
										</thead>
										<tbody class="fs-12px">
										<?php foreach ($gaming_keybinds as $keybind_item): ?>
											<tr>
												<td class="text-start"><?php echo htmlspecialchars($keybind_item->note); ?></td>
												<td><?php echo htmlspecialchars($keybind_item->keybind); ?></td>
											</tr>
										<?php endforeach; ?>
										</tbody>
									</table>
								</div>
							<?php else: ?>
								<p>No keybinds available for this game.</p>
							<?php endif; ?>
						</div>
						<?php #endregion Left Half ?>
						<?php #region Right Half: Display Issues (HTML Rendered, Read-Only) ?>
						<div class="col-md-6 col-xs-12" >
							<h5>Game Issues</h5>
							<div class="p-2 border rounded" style="background-color: var(--bs-body-bg); color: var(--bs-body-color);">
								<?php echo $modgaming->issues; // Render HTML content directly ?>
							</div>
						</div>
						<?php #endregion Right Half ?>
					</div>
				</div>
				<?php #endregion TAB keybinds ?>
				<?php #region region TAB issues notas ?>
				<div class="tab-pane fade <?php echo ($tabselected == 3) ? "active show" : ""; ?>" id="default-tab-3">
					<!-- BEGIN row -->
					<div class="row mt-3 mb-2">
						<?php #region region SUBMIT sub_mod_issues ?>
						<div class="col-md-12 col-xs-12">
							<button type="submit" id="sub_mod_issues" name="sub_mod_issues" class="btn btn-xs btn-success w-100 no-border-radious">
								Modificar
							</button>
						</div>
						<?php #endregion SUBMIT sub_mod_issues ?>
					</div>
					<!-- END row -->
					<textarea id="issues" name="issues" class="ckeditor">
                        <?php echo $modgaming->issues; ?>
                    </textarea>
				</div>
				<?php #endregion region TAB issues notas ?>
			</div>
			<?php #endregion NAVTAB gaming ?>
			<?php #region region MODAL mdl_revisar_keybind ?>
			<div class="modal fade" id="mdl_revisar_keybind">
				<div class="modal-dialog modal-lg modal-dialog-centered">
					<div class="modal-content">
						<input type="hidden" id="mdl_revisar_keybind_id_keybind" name="mdl_revisar_keybind_id_keybind">
						
						<div class="modal-header">
							<h4 class="modal-title">Revisar keybind</h4>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
						</div>
						<div class="modal-body">
							<!-- BEGIN text -->
							<div class="col-md-12 col-xs-12">
								<div class="mb-3">
									<label class="form-label">Nota:</label>
									<input type="text" name="mdl_revisar_keybind_nota" id="mdl_revisar_keybind_nota" class="form-control" onclick="this.focus();this.select('')"/>
								</div>
							</div>
							<!-- END text -->
							<!-- BEGIN text -->
							<div class="col-md-12 col-xs-12">
								<div class="mb-3">
									<label class="form-label">Keybind:</label>
									<input type="text" name="mdl_revisar_keybind_keybind" id="mdl_revisar_keybind_keybind" class="form-control" onclick="this.focus();this.select('')"/>
								</div>
							</div>
							<!-- END text -->
							<!-- BEGIN text -->
							<div class="col-md-12 col-xs-12">
								<div class="mb-3">
									<label class="form-label">Prioridad:</label>
									<input type="text" name="mdl_revisar_keybind_prioridad" id="mdl_revisar_keybind_prioridad" onclick="this.focus();this.select('')" class="form-control"/>
								</div>
							</div>
							<!-- END text -->
							<!-- BEGIN row -->
							<div class="row mt-3">
								<?php #region region SUBMIT sub_editar_keybind ?>
								<div class="col-md-12 col-xs-12">
									<button type="submit" id="sub_editar_keybind" name="sub_editar_keybind" class="btn btn-md btn-success w-100">
										Modificar
									</button>
								</div>
								<?php #endregion SUBMIT sub_editar_keybind ?>
							</div>
							<!-- END row -->
							<!-- BEGIN row -->
							<div class="row mt-3">
								<?php #region region SUBMIT sub_del_keybind ?>
								<div class="col-md-12 col-xs-12">
									<button type="submit" id="sub_del_keybind" name="sub_del_keybind" class="btn btn-md btn-danger w-100">
										Eliminar keybind
									</button>
								</div>
								<?php #endregion SUBMIT sub_del_keybind ?>
							</div>
							<!-- END row -->
						</div>
					</div>
				</div>
			</div>
			<?php #endregion MODAL mdl_revisar_keybind ?>
			<?php #region region MODAL mdl_desinstalar ?>
			<div class="modal fade" id="mdl_desinstalar">
				<div class="modal-dialog modal-lg modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">Desinstalar</h4>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
						</div>
						<div class="modal-body">
							<p>Esta seguro que desea desinstalar este juego?</p>
							<!-- BEGIN row -->
							<div class="row mt-3">
								<?php #region region SUBMIT sub_desinstalar ?>
								<div class="col-md-12 col-xs-12">
									<button type="submit" id="sub_desinstalar" name="sub_desinstalar" class="btn btn-md btn-danger w-100">
										Desinstalar
									</button>
								</div>
								<?php #endregion SUBMIT sub_eliminar ?>
							</div>
							<!-- END row -->
							<!-- BEGIN row -->
							<div class="row mt-3">
								<?php #region region LINK cancelar ?>
								<div class="col-md-12 col-xs-12">
									<a href="#" class="btn btn-md btn-default w-100" data-bs-dismiss="modal">
										Cancelar
									</a>
								</div>
								<?php #endregion LINK cancelar ?>
							</div>
							<!-- END row -->
						</div>
					</div>
				</div>
			</div>
			<?php #endregion MODAL mdl_desinstalar ?>
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php
// Check if success message is for keybind modification
$is_keybind_success = !empty($success_text) && $success_text === 'El keybind ha sido modificado.';

// If it's keybind success, temporarily clear success_text to prevent SweetAlert
$original_success_text = $success_text ?? '';
if ($is_keybind_success) {
	$success_text = '';
}
?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
<?php
// Restore original success text for potential other uses
$success_text = $original_success_text;
?>

<?php #region region JS ckeditor ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/ckeditor/ckeditor.js"></script>
<script>
    CKEDITOR.replace('issues', {
        height     : 8000, // Set height in pixels
        contentsCss: ['<?php echo RUTA ?>resources/css/fab_ckeditor.css'],
    });
</script>
<?php #endregion JS ckeditor ?>
<?php #region region JS tabselect ?>
<script type="text/javascript">
    function tabselect(ntab) {
        document.getElementById('tabselected').value = ntab;
    }
</script>
<?php #endregion JS tabselect ?>
<?php #region region JS MODAL mdl_revisar_keybind ?>
<script type="text/javascript">
    $('#mdl_revisar_keybind').on('shown.bs.modal', function (event) {
        const button               = $(event.relatedTarget);
        const recipient_id_keybind = button.data('id_keybind');
        const recipient_nota       = button.data('nota');
        const recipient_keybind    = button.data('keybind');
        const recipient_prioridad  = button.data('prioridad');
        
        const mdl_revisar_keybind_id_keybind = document.getElementById('mdl_revisar_keybind_id_keybind');
        const mdl_revisar_keybind_nota       = document.getElementById('mdl_revisar_keybind_nota');
        const mdl_revisar_keybind_keybind    = document.getElementById('mdl_revisar_keybind_keybind');
        const mdl_revisar_keybind_prioridad  = document.getElementById('mdl_revisar_keybind_prioridad');
        
        mdl_revisar_keybind_id_keybind.value = recipient_id_keybind;
        mdl_revisar_keybind_nota.value       = recipient_nota;
        mdl_revisar_keybind_keybind.value    = recipient_keybind;
        mdl_revisar_keybind_prioridad.value  = recipient_prioridad;
    })
</script>
<?php #endregion JS MODAL mdl_revisar_keybind ?>

<?php #region region JS select choices.js ?>
<script src="<?php echo RUTA ?>resources/choices.js/choices.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const selectElements = document.querySelectorAll('.form-select-choices');
        
        selectElements.forEach((element) => {
            const choices = new Choices(element, {
                searchEnabled   : true, // Enable search functionality
                removeItemButton: true, // Allow removing selected items in multi-select
                shouldSort      : false // Disable sorting of options
            });
        });
    });
</script>
<?php #endregion JS select choices.js ?>
<?php #region region JS Standby Input Control ?>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        const standbySwitch = document.getElementById('en_standby');
        const standbyTexto  = document.getElementById('texto_standby');
        
        // Función para actualizar el estado del input de texto
        function updateTextoStandbyState() {
            if (standbySwitch.checked) {
                standbyTexto.disabled = false; // Habilitar input
            } else {
                standbyTexto.disabled = true;  // Deshabilitar input
                // Opcional: Limpiar el campo de texto cuando se deshabilita
                // standbyTexto.value = '';
            }
        }
        
        // Añadir listener para cuando cambie el switch
        if (standbySwitch && standbyTexto) {
            standbySwitch.addEventListener('change', updateTextoStandbyState);
            
            // Llamar una vez al cargar la página para establecer el estado inicial
            updateTextoStandbyState();
        } else {
            console.error("No se encontraron los elementos #en_standby o #texto_standby");
        }
    });
</script>
<?php #endregion JS Standby Input Control ?>
<?php #region region JS Input Method Validation ?>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        const form = document.querySelector('form[action="egaming"]');
        const submitButton = document.getElementById('sub_mod');

        // Client-side validation for input method
        function validateInputMethod() {
            const inputRadios = document.querySelectorAll('input[name="input"]');
            let isSelected = false;

            inputRadios.forEach(function(radio) {
                if (radio.checked) {
                    isSelected = true;
                }
            });

            return isSelected;
        }

        // Add validation on form submit
        if (form && submitButton) {
            form.addEventListener('submit', function(e) {
                if (e.submitter === submitButton) {
                    if (!validateInputMethod()) {
                        e.preventDefault();
                        alert('Por favor selecciona un método de entrada (Teclado + Mouse o Control).');
                        return false;
                    }
                }
            });
        }

        // Visual feedback for radio button selection
        const inputRadios = document.querySelectorAll('input[name="input"]');
        inputRadios.forEach(function(radio) {
            radio.addEventListener('change', function() {
                // Remove any previous error styling
                const container = document.querySelector('.btn-group[data-toggle="buttons"]');
                if (container) {
                    container.classList.remove('border-danger');
                }
            });
        });
    });
</script>
<?php #endregion JS Input Method Validation ?>
<?php #region region JS Keybind UX Enhancements ?>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        // Enhancement 1: Enter key submission for keybind inputs
        const noteKeybindInput = document.getElementById('note_keybind');
        const keybindKeybindInput = document.getElementById('keybind_keybind');
        const addKeybindButton = document.getElementById('sub_add_keybind');

        // Function to handle Enter key press
        function handleEnterKeyPress(event) {
            if (event.key === 'Enter' || event.keyCode === 13) {
                event.preventDefault(); // Prevent default form submission
                if (addKeybindButton) {
                    addKeybindButton.click(); // Trigger the button click
                }
            }
        }

        // Add event listeners for Enter key on both input fields
        if (noteKeybindInput) {
            noteKeybindInput.addEventListener('keypress', handleEnterKeyPress);
        }
        if (keybindKeybindInput) {
            keybindKeybindInput.addEventListener('keypress', handleEnterKeyPress);
        }

        // Enhancement 2: Conditional auto-focus after keybind submission
        <?php if (isset($keybind_added_successfully) && $keybind_added_successfully === true): ?>
        // Auto-focus on note_keybind field only when keybind was successfully added
        if (noteKeybindInput) {
            noteKeybindInput.focus();
        }

        // Show toast notification for successful keybind addition
        showToastMessage('El keybind ha sido agregado.');
        <?php endif; ?>

        // Enhancement 3: Show toast notification for successful keybind modification
        <?php if ($is_keybind_success): ?>
        // Show toast notification for successful keybind modification
        showToastMessage('El keybind ha sido modificado.');
        <?php endif; ?>
    });

    // Toast notification function (from epartido_probabilidades.view.php)
    function showToastMessage(message) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.classList.add('floating-badge-toast');
        document.body.appendChild(toast);
        setTimeout(() => { toast.classList.add('show'); }, 10);
        setTimeout(() => {
            toast.classList.add('hide');
            setTimeout(() => { toast.remove(); }, 300);
        }, 2000);
    }
</script>
<?php #endregion JS Keybind UX Enhancements ?>
<?php #endregion JS ?>

</body>
</html>