<?php

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

global $conexion;

use App\classes\Allocation;
use App\classes\AllocationItem;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';
require_once __ROOT__ . '/src/classes/Allocation.php';
require_once __ROOT__ . '/src/classes/AllocationItem.php';

#region AJAX Transfer Handler
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'transfer_valor_bolsillo') {
    // Prevent any output before JSON response
    ob_clean();
    header('Content-Type: application/json');

    try {
        // Get and validate input parameters
        $sourceId = limpiar_datos($_POST['source_id'] ?? '');
        $destinationId = limpiar_datos($_POST['destination_id'] ?? '');
        $amount = limpiar_datos($_POST['amount'] ?? '');

        if (empty($sourceId)) {
            throw new Exception('ID de origen requerido');
        }

        if (empty($destinationId)) {
            throw new Exception('ID de destino requerido');
        }

        if (empty($amount)) {
            throw new Exception('Monto requerido');
        }

        // Convert Colombian peso format to numeric (3.500 -> 3500)
        // First remove dots (thousands separators) then clean
        $amountWithoutDots = str_replace('.', '', $amount);
        $numericAmount = (float)format_numberclean($amountWithoutDots);

        if ($numericAmount <= 0) {
            throw new Exception('El monto debe ser mayor a cero');
        }

        // Perform the transfer
        AllocationItem::transfer($sourceId, $destinationId, $numericAmount, $conexion);

        // Get updated values for response
        $sourceItem = AllocationItem::get($sourceId, $conexion);
        $destinationItem = AllocationItem::get($destinationId, $conexion);

        $response = [
            'status' => 'success',
            'message' => 'Transferencia realizada exitosamente',
            'sourceNewValue' => $sourceItem ? (float)$sourceItem->getValorBolsillo() : 0,
            'destinationNewValue' => $destinationItem ? (float)$destinationItem->getValorBolsillo() : 0
        ];

        echo json_encode($response);
        exit();

    } catch (Exception $e) {
        $errorResponse = [
            'status' => 'error',
            'message' => $e->getMessage()
        ];

        echo json_encode($errorResponse);
        exit();
    }
}
#endregion AJAX Transfer Handler

// Page variables
$allocations        = [];     // Allocation[] active allocations
$itemsByAllocation  = [];     // map desordenado allocation id => AllocationItem[]
$allItems           = [];     // All allocation items for transfer dropdowns

#region try
try {
    // Load active allocations and their items from database (saved state)
    $allocations = Allocation::getList($conexion);
    foreach ($allocations as $allocation) {
        $aid = $allocation->getId(); // desordenado string id
        $itemsByAllocation[$aid] = AllocationItem::getByAllocation($aid, $conexion);
    }

    // Load all allocation items for transfer dropdowns
    $allItems = AllocationItem::getListWithAllocation($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/rallocations.view.php';

?>
