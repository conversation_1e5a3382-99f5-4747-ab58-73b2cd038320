<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/general/preparar.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';
require_once __ROOT__ . '/src/classes/PaisesTorneoFootyApi.php';
require_once __ROOT__ . '/src/classes/paisseason.php';

#region ajax_process_team_tournament
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_process_team_tournament'])) {
    header('Content-Type: application/json');

    try {
        $team_id = limpiar_datos($_POST['team_id']);

        // Validate input
        if (empty($team_id)) {
            throw new Exception('Team ID is required');
        }

        // Get active betting seasons from configuration
        $config = Config::get_all_configs_page($conexion);
        $active_seasons = [];
        if (!empty($config->bet_season_1)) {
            $active_seasons[] = strtolower(trim($config->bet_season_1));
        }
        if (!empty($config->bet_season_2)) {
            $active_seasons[] = strtolower(trim($config->bet_season_2));
        }

        // Build API URL
        $apiKey = FTS_API;
        $apiUrl = "https://api.football-data-api.com/team?key={$apiKey}&team_id={$team_id}";

        // Make API call
        $response = @file_get_contents($apiUrl);
        if ($response === false) {
            throw new Exception('Error connecting to API');
        }

        $data = json_decode($response, true);
        if (!$data || !isset($data['data'])) {
            throw new Exception('Invalid API response');
        }

        // Extract unique competition IDs
        $competitionIds = [];
        if (is_array($data['data'])) {
            foreach ($data['data'] as $item) {
                if (isset($item['competition_id'])) {
                    $competitionIds[] = $item['competition_id'];
                }
            }
        }
        $competitionIds = array_unique($competitionIds);

        // Array to store tournaments for this team
        $team_tournaments = [];

        // Cache for encoded pais IDs to avoid multiple desordena() calls for same ID
        $encoded_pais_cache = [];

        // Map competition IDs to country names and seasons
        foreach ($competitionIds as $competitionId) {
            // Query PaisesTorneoFootyApi to find matching record
            $query_api = "SELECT ptfa.id_pais, ptfa.season
                          FROM paises_torneos_footy_api ptfa
                          WHERE ptfa.id_footy = :competition_id
                          LIMIT 1";
            $statement_api = $conexion->prepare($query_api);
            $statement_api->bindValue(":competition_id", $competitionId);
            $statement_api->execute();
            $resultado_api = $statement_api->fetch();

            if ($resultado_api) {
                $season = $resultado_api['season'] ?? '';

                // Filter by active season
                if (!empty($season) && in_array(strtolower(trim($season)), $active_seasons)) {
                    // Get country/tournament info
                    $pais = Pais::get(desordena($resultado_api['id_pais']), $conexion);

                    if (!empty($pais->nombre)) {
                        // Use cached encoded ID or create new one
                        $raw_pais_id = (string)$resultado_api['id_pais'];
                        if (!isset($encoded_pais_cache[$raw_pais_id])) {
                            $encoded_pais_cache[$raw_pais_id] = desordena($raw_pais_id);
                        }
                        $encoded_pais_id = $encoded_pais_cache[$raw_pais_id];
                        $unique_key = $encoded_pais_id . '_' . $season;

                        if (!isset($team_tournaments[$unique_key])) {
                            // Get fecha_upload from partidos_info (DB stores numeric id_pais)
                            $query_upload = "SELECT IFNULL(pi.fecha_upload, '') AS fecha_upload
                                             FROM partidos_info pi
                                             WHERE pi.id_pais = :id_pais
                                               AND pi.season = :season
                                               AND pi.status = 'COMPLETE'
                                             LIMIT 1";
                            $statement_upload = $conexion->prepare($query_upload);
                            $statement_upload->bindValue(":id_pais", $resultado_api['id_pais']);
                            $statement_upload->bindValue(":season", $season);
                            $statement_upload->execute();
                            $resultado_upload = $statement_upload->fetch();

                            $fecha_upload = $resultado_upload ? $resultado_upload['fecha_upload'] : '';

                            // Get Footy ID for this team tournament
                            $footy_id = '';
                            try {
                                $footy_api_record = \App\classes\PaisesTorneoFootyApi::getByPaisAndSeason(
                                    (int)$resultado_api['id_pais'],
                                    $season,
                                    $conexion
                                );
                                if ($footy_api_record) {
                                    $footy_id = $footy_api_record->getIdFooty();
                                }
                            } catch (Exception $e) {
                                error_log("Error getting Footy ID for team tournament: " . $e->getMessage());
                            }

                            // Only include team tournaments that have a Footy ID
                            if (!empty($footy_id)) {
                                // Create tournament object for JSON response
                                $team_tournaments[$unique_key] = [
                                    'pais' => $pais->nombre,
                                    // Send encoded ID to frontend, consistent with other views
                                    'pais_id' => $encoded_pais_id,
                                    'raw_pais_id' => $raw_pais_id, // Add raw ID for deduplication
                                    'season' => $season,
                                    'fecha_upload' => $fecha_upload,
                                    'unique_key' => $unique_key,
                                    'footy_id' => $footy_id
                                ];
                            }
                        }
                    }
                }
            }
        }

        echo json_encode([
            'status' => 'success',
            'team_id' => $team_id,
            'tournaments' => array_values($team_tournaments)
        ]);
        exit();

    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'team_id' => $_POST['team_id'] ?? '',
            'message' => $e->getMessage()
        ]);
        exit();
    }
}
#endregion ajax_process_team_tournament

#region sub_actualizar_via_api
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_actualizar_via_api'])) {
    // Return JSON response for AJAX
    header('Content-Type: application/json');

    try {
        $pais_id_raw = limpiar_datos($_POST['pais_id']);
        $season = limpiar_datos($_POST['season']);
        $footy_id = limpiar_datos($_POST['footy_id']);

        // Decode pais_id (use ordena to decrypt encoded ID back to integer)
        $pais_id_decoded = $pais_id_raw;
        if (function_exists('ordena')) {
            try { $pais_id_decoded = ordena($pais_id_raw); } catch (Exception $ex) { /* keep raw */ }
        }
        $pais_id = (int)$pais_id_decoded;

        // Validate inputs
        if ($pais_id <= 0 || empty($season) || empty($footy_id)) {
            throw new Exception('Faltan parámetros requeridos');
        }

        $conexion->beginTransaction();

        // Delete existing records for this country and season before importing new ones
        $delete_params = array();
        // PartidoInfo::deleteSelected expects 'desordenado' (encoded) idpais
        $delete_params['idpais'] = $pais_id_raw;
        $delete_params['season'] = $season;
        PartidoInfo::deleteSelected($delete_params, $conexion);

        // Create date with Bogotá timezone (create_date() already calls setTimeZoneCol() internally)
        $fecha_upload = create_date();

        $records_created = 0;
        $page = 1;
        $api_key = FTS_API;

        do {
            // Build API URL for this page (mirror reference implementation)
            $api_url = "https://api.football-data-api.com/league-matches?key={$api_key}&season_id={$footy_id}&max_per_page=1000&page={$page}";

            // Make API call with detailed logging
            error_log("API URL: " . $api_url);
            $response = @file_get_contents($api_url);
            if ($response === false) {
                error_log("API call failed for URL: " . $api_url);
                throw new Exception('Error al conectar con la API de Football Data');
            }

            error_log("API Response (first 500 chars): " . substr($response, 0, 500));
            $data = json_decode($response, true);
            if (!$data || !isset($data['success']) || !$data['success']) {
                error_log("Invalid API response (no success flag or success=false). Raw: " . $response);
                throw new Exception('Respuesta inválida de la API');
            }

            // If the API returns data=null, it means no more pages
            if (array_key_exists('data', $data) && $data['data'] === null) {
                error_log("API returned null data on page {$page}, stopping pagination");
                break;
            }

            error_log("API page {$page} returned " . (is_array($data['data']) ? count($data['data']) : '0') . " matches");

            // Process matches from this page
            if (is_array($data['data']) && count($data['data']) > 0) {
                foreach ($data['data'] as $match) {
                    // Only save records where status = "complete" (same as source)
                    if (strtolower($match['status']) !== 'complete') {
                        continue;
                    }

                    // Create new PartidoInfo record
                    $newpartidoinfo = new PartidoInfo();

                    // Convert Unix timestamp to yyyy-MM-dd format
                    $newpartidoinfo->fecha = date('Y-m-d', $match['date_unix']);
                    $newpartidoinfo->status = $match['status'];
                    $newpartidoinfo->home = $match['home_name'];
                    $newpartidoinfo->away = $match['away_name'];
                    $newpartidoinfo->homegoals = (int)$match['homeGoalCount'];
                    $newpartidoinfo->awaygoals = (int)$match['awayGoalCount'];
                    $newpartidoinfo->homecorners = (int)$match['team_a_corners'];
                    $newpartidoinfo->awaycorners = (int)$match['team_b_corners'];
                    $newpartidoinfo->homeshots = (int)$match['team_a_shots'];
                    $newpartidoinfo->awayshots = (int)$match['team_b_shots'];
                    $newpartidoinfo->homeshotstarget = (int)$match['team_a_shotsOnTarget'];
                    $newpartidoinfo->awayshotstarget = (int)$match['team_b_shotsOnTarget'];
                    $newpartidoinfo->homeposession = (int)$match['team_a_possession'];
                    $newpartidoinfo->awayposession = (int)$match['team_b_possession'];
                    $newpartidoinfo->pais = new Pais();
                    // PartidoInfo::add expects pais->id as 'desordenado' (encoded)
                    $newpartidoinfo->pais->id = $pais_id_raw;
                    $newpartidoinfo->season = $season;
                    $newpartidoinfo->fecha_upload = $fecha_upload;

                    $newpartidoinfo->add($conexion);
                    $records_created++;
                }

                $page++;
            }

        } while ($data['data'] !== null);

        // Update pais season
        $nom_pais = Pais::get($pais_id_raw, $conexion)->nombre;
        $param = array();
        $param['nom_pais'] = $nom_pais;
        $param['season'] = $season;
        PartidoInfo::add_pais_season($param, $conexion);

        $conexion->commit();

        echo json_encode([
            'status' => 'success',
            'message' => 'Datos actualizados exitosamente via API',
            'records_created' => $records_created,
            'fecha_upload' => $fecha_upload,
            'pais_id' => $pais_id_raw,
            'raw_pais_id' => $pais_id, // Add raw database ID for row updates
            'season' => $season
        ]);
        exit();

    } catch (Exception $e) {
        if ($conexion->inTransaction()) {
            $conexion->rollback();
        }

        // Log the full error details
        error_log("API Update Error: " . $e->getMessage());
        error_log("API Update Error Trace: " . $e->getTraceAsString());
        error_log("API Update Parameters - pais_id_raw: " . ($pais_id_raw ?? 'n/a') . ", pais_id_decoded: " . ($pais_id ?? 'n/a') . ", season: {$season}, footy_id: {$footy_id}");

        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage(),
            'debug_info' => [
                'pais_id_raw' => $pais_id_raw ?? 'not set',
                'pais_id' => $pais_id ?? 'not set',
                'season' => $season ?? 'not set',
                'footy_id' => $footy_id ?? 'not set',
                'error_line' => $e->getLine(),
                'error_file' => $e->getFile()
            ]
        ]);
        exit();
    }
}
#endregion sub_actualizar_via_api

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        // Handle any GET parameters if needed
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get

#region try
try {
    // Get active betting seasons from configuration
    $config = Config::get_all_configs_page($conexion);

    // Get tournaments from pending matches (existing logic)
    $torneos_raw = Partido::get_torneos_pendientes_revision($conexion);

    // DEBUG: Log what we got from database
    error_log("DEBUG: Raw tournaments count: " . count($torneos_raw));
    foreach ($torneos_raw as $t) {
        error_log("DEBUG: Tournament - ID: " . ($t->pais_torneo->raw_id ?? 'NULL') . ", Season: " . ($t->season ?? 'NULL') . ", Name: " . ($t->pais ?? 'NULL'));
    }

    // DEDUPLICATION: Group by id_pais and season only
    $torneos_unicos = [];
    $seen_tournaments = [];

    foreach ($torneos_raw as $torneo) {
        $id_pais = $torneo->pais_torneo->raw_id;
        $season = $torneo->season;

        // Create key using ONLY id_pais and season
        $key = $id_pais . '|' . $season;

        if (!isset($seen_tournaments[$key])) {
            $seen_tournaments[$key] = true;
            $torneos_unicos[] = $torneo;
            error_log("DEBUG: KEEPING tournament - ID: $id_pais, Season: $season");
        } else {
            error_log("DEBUG: SKIPPING duplicate - ID: $id_pais, Season: $season");
        }
    }

    error_log("DEBUG: After deduplication count: " . count($torneos_unicos));
    $torneos = $torneos_unicos;

    // Get unique team IDs from all pending matches
    $fecha = create_date();
    $query = "SELECT DISTINCT p.id_home, p.id_away
              FROM partidos p
              WHERE p.estado = 1
                AND p.revisado_probabilidades = 0
                AND p.fecha >= :fecha
                AND (p.id_home IS NOT NULL OR p.id_away IS NOT NULL)";

    $statement = $conexion->prepare($query);
    $statement->bindValue(":fecha", $fecha);
    $statement->execute();
    $resultados = $statement->fetchAll();

    // Collect unique team IDs
    $team_ids = [];
    foreach ($resultados as $resultado) {
        if (!empty($resultado['id_home'])) {
            $team_ids[] = $resultado['id_home'];
        }
        if (!empty($resultado['id_away'])) {
            $team_ids[] = $resultado['id_away'];
        }
    }
    $team_ids = array_unique($team_ids);

    // Calculate statistics for the loading interface
    $total_pending_matches = count($resultados);
    $total_unique_teams = count($team_ids);

    // Enrich initial tournament data with Footy IDs and filter out those without Footy ID
    $torneos_filtered = [];
    $unique_tournaments = []; // Track unique país-season combinations

    foreach ($torneos as $torneo) {
        // Create unique key for deduplication
        $unique_key = $torneo->pais_torneo->id . '_' . $torneo->season;

        // Skip if we already have this país-season combination
        if (isset($unique_tournaments[$unique_key])) {
            continue;
        }

        // Get Footy ID for this tournament
        $torneo->footy_id = '';
        try {
            if (!empty($torneo->season)) {
                $footy_api_record = \App\classes\PaisesTorneoFootyApi::getByPaisAndSeason(
                    (int)ordena($torneo->pais_torneo->id),
                    $torneo->season,
                    $conexion
                );
                if ($footy_api_record) {
                    $torneo->footy_id = $footy_api_record->getIdFooty();
                }
            }
        } catch (Exception $e) {
            // Log error but don't break the page
            error_log("Error getting Footy ID: " . $e->getMessage());
        }

        // Only include tournaments that have a Footy ID
        if (!empty($torneo->footy_id)) {
            // Add raw ID for JavaScript deduplication
            $torneo->raw_pais_id = $torneo->pais_torneo->raw_id;

            // Calculate date-related properties for display
            $torneo->text_color_class = '';
            $torneo->days_diff = null;
            if (!empty($torneo->fecha_upload)) {
                setTimeZoneCol(); // Set Bogotá timezone
                $fecha_actual = create_date();
                $torneo->days_diff = getDateDiffDaysNonLiteral($torneo->fecha_upload, $fecha_actual);

                // Apply color logic: green for 0-6 days, gray for 7+ days
                if ($torneo->days_diff >= 0 && $torneo->days_diff <= 6) {
                    $torneo->text_color_class = 'text-success';
                } else {
                    $torneo->text_color_class = 'text-muted';
                }
            }

            // Add to filtered list and mark as processed
            $torneos_filtered[] = $torneo;
            $unique_tournaments[$unique_key] = true;
        }
    }

    // Replace original array with filtered array
    $torneos = $torneos_filtered;

    // Pass data to view for AJAX processing
    $team_ids_json = json_encode($team_ids);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/rtorneos.view.php';

?>

